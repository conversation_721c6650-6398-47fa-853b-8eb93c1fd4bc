// components/SitePreview.js
import React, { useState } from "react";
import {
  View, Text, TextInput, Pressable, Image, ActivityIndicator
} from "react-native";
import { OPENAI_PROXY } from "../lib/api";
import { getLogoDataUrl } from "../lib/logo";

const Field = ({ label, children }) => (
  <View style={{ marginBottom: 12 }}>
    <Text style={{ color: "#111827", marginBottom: 6, fontWeight: "600" }}>{label}</Text>
    {children}
  </View>
);

const Button = ({ title, onPress, disabled }) => (
  <Pressable
    onPress={onPress}
    disabled={disabled}
    style={({ pressed }) => ({
      backgroundColor: disabled ? "#c7d2fe" : "#6366F1",
      paddingVertical: 10, paddingHorizontal: 14,
      borderRadius: 10, opacity: pressed ? 0.85 : 1, marginRight: 8
    })}
  >
    <Text style={{ color: "white", fontWeight: "700" }}>{title}</Text>
  </Pressable>
);

export default function SitePreview() {
  const [domain, setDomain] = useState("studentpharm.co.ke");
  const [businessName, setBusinessName] = useState("StudentPharm");
  const [logoPrompt, setLogoPrompt] = useState("pill + book icon, purple gradient");
  const [templateStyle, setTemplateStyle] = useState("colorful");
  const [logoUrl, setLogoUrl] = useState("");
  const [files, setFiles] = useState(null);
  const [busy, setBusy] = useState(false);
  const [msg, setMsg] = useState("");

  const effectivePrompt = () =>
    (logoPrompt && logoPrompt.trim()) ||
    (businessName && `${businessName} logo`) ||
    "simple geometric mark";

  async function onGenerateLogo() {
    try {
      setBusy(true);
      const url = await getLogoDataUrl(effectivePrompt()); // AI or placeholder if 402/limit
      setLogoUrl(url);
      setMsg("Logo ready.");
    } catch (e) {
      setMsg(String(e?.message || e));
    } finally {
      setBusy(false);
    }
  }

  async function onGenerateSite() {
    try {
      setBusy(true);
      const ensuredLogo = logoUrl || await getLogoDataUrl(effectivePrompt());
      const res = await fetch(`${OPENAI_PROXY}/v1/sites/generate`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          domain,
          businessName,
          templateStyle, // colorful | cool | minimal | sleek | playful
          asZip: false,
          logoDataUrl: ensuredLogo
        })
      });
      const j = await res.json();
      if (!res.ok) throw new Error(j?.error?.message || `HTTP ${res.status}`);
      setFiles(j.files || {});
      setMsg("Site generated from templates.");
    } catch (e) {
      setMsg(String(e?.message || e));
    } finally {
      setBusy(false);
    }
  }

  return (
    <View style={{ backgroundColor: "#ffffff", borderRadius: 16, padding: 16,
                   shadowColor: "#000", shadowOpacity: 0.06, shadowRadius: 8, elevation: 2 }}>
      <Field label="Business name">
        <TextInput
          value={businessName} onChangeText={setBusinessName}
          style={{ backgroundColor: "#eef2ff", padding: 10, borderRadius: 8 }}
        />
      </Field>

      <Field label="Domain">
        <TextInput
          value={domain} onChangeText={setDomain}
          style={{ backgroundColor: "#eef2ff", padding: 10, borderRadius: 8 }}
        />
      </Field>

      <Field label="Template style (colorful | cool | minimal | sleek | playful)">
        <TextInput
          value={templateStyle} onChangeText={setTemplateStyle}
          style={{ backgroundColor: "#eef2ff", padding: 10, borderRadius: 8 }}
        />
      </Field>

      <Field label="Logo prompt">
        <TextInput
          value={logoPrompt} onChangeText={setLogoPrompt}
          placeholder="e.g., 'pill + book icon, purple gradient'"
          style={{ backgroundColor: "#eef2ff", padding: 10, borderRadius: 8 }}
        />
      </Field>

      <View style={{ flexDirection: "row", alignItems: "center", marginBottom: 12 }}>
        <Button title="Generate logo" onPress={onGenerateLogo} disabled={busy} />
        <Button title="Generate site" onPress={onGenerateSite} disabled={busy} />
        {busy ? <ActivityIndicator style={{ marginLeft: 8 }} /> : null}
      </View>

      {logoUrl ? (
        <Image
          source={{ uri: logoUrl }}
          style={{ width: 96, height: 96, borderRadius: 16, marginBottom: 12 }}
          resizeMode="cover"
        />
      ) : null}

      {msg ? <Text style={{ color: "#374151", marginBottom: 12 }}>{msg}</Text> : null}

      {files ? (
        <View style={{ backgroundColor: "#f3f4f6", padding: 10, borderRadius: 8 }}>
          <Text selectable style={{ fontFamily: "monospace", fontSize: 12 }}>
            {JSON.stringify(files, null, 2)}
          </Text>
        </View>
      ) : null}
    </View>
  );
}
