import React, { useEffect, useState, useContext } from "react";
import { View, Text, TextInput, ActivityIndicator } from "react-native";
import AppState from "../context/AppState";
import PrimaryButton from "./PrimaryButton";
import DomainPicker from "./DomainPicker";
import Recommendations from "./Recommendations";
import { suggestDomains, businessCopy, buildWebsite } from "../lib/ai";
import { checkBatch } from "../lib/availability";

export default function DomainSuggester(){
  const { group, pickedDomain, setPickedDomain, selectedTemplate, siteConfig, setSiteConfig } = useContext(AppState);
  const [idea, setIdea] = useState("");
  const [domains, setDomains] = useState([]);
  const [status, setStatus] = useState({});
  const [loading, setLoading] = useState(false);
  const [copy, setCopy] = useState(null);

  const onPickIdea = (t) => setIdea(t);

  async function onSuggest(){
    setLoading(true);
    setDomains([]);
    try {
      const res = await suggestDomains({ idea, group });
      setDomains(res.domains || []);
    } finally { setLoading(false); }
  }

  useEffect(() => {
    let ok = true;
    if (domains.length) {
      checkBatch(domains).then(s => ok && setStatus(s));
    }
    return () => { ok = false; };
  }, [domains]);

  async function onPick(d){
    setPickedDomain(d);
    const en = await businessCopy({ domain: d, lang:"en", group });
    const sw = await businessCopy({ domain: d, lang:"sw", group });
    setCopy({ en, sw });
  }

  async function onGenerate(){
    const enabledPages = Object.entries(siteConfig.pages || {})
      .filter(([,v]) => v).map(([k]) => k);
    const built = await buildWebsite({
      domain: pickedDomain,
      template: selectedTemplate,
      theme: siteConfig?.theme,
      pages: enabledPages,
      group
    });
    setSiteConfig({ ...siteConfig, built });
  }

  return (
    <View style={{ gap: 12 }}>
      <Text style={{ fontWeight:"700", fontSize:16 }}>Suggest .KE domains</Text>

      <Recommendations onPick={onPickIdea} />

      <TextInput
        placeholder="Describe your idea..."
        value={idea}
        onChangeText={setIdea}
        style={{ borderWidth:1, padding:10, borderRadius:8 }}
      />

      <PrimaryButton label="Suggest" onPress={onSuggest} />
      {loading && <ActivityIndicator />}

      <DomainPicker domains={domains} status={status} selected={pickedDomain} onPick={onPick} />

      <PrimaryButton label={`Generate website (${selectedTemplate})`} onPress={onGenerate} disabled={!pickedDomain} />

      {copy ? (
        <View style={{ borderWidth:1, borderRadius:10, padding:10 }}>
          <Text style={{ fontWeight:"700" }}>EN</Text>
          <Text>{copy.en?.headline}</Text>
          <Text>{copy.en?.subheadline}</Text>
          <Text style={{ marginTop:6, fontWeight:"700" }}>SW</Text>
          <Text>{copy.sw?.headline}</Text>
          <Text>{copy.sw?.subheadline}</Text>
        </View>
      ) : null}
    </View>
  );
}
