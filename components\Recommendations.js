import React, { useMemo, useContext } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import AppState from "../context/AppState";
const presets = {
  student_startup: ["Campus pharmacy deals", "Budget study supplies", "Club merch store"],
  sme: ["Local boutique fashion", "Electronics repair hub", "Office stationery"],
  creative: ["Portfolio for illustrator", "Studio bookings", "Print-on-demand art"],
  nonprofit: ["Community health clinic", "Afterschool program", "Donations site"],
  ecommerce: ["Daily essentials store", "Beauty & self-care", "Smart accessories"]
};
export default function Recommendations({ onPick }){
  const { group } = useContext(AppState);
  const ideas = useMemo(() => presets[group] || [], [group]);
  if (!ideas.length) return null;
  return (
    <View style={{ gap: 6 }}>
      <Text style={{ fontWeight:"600" }}>Quick ideas</Text>
      <View style={{ flexDirection:"row", flexWrap:"wrap" }}>
        {ideas.map((t) => (
          <TouchableOpacity key={t} onPress={()=>onPick(t)}
            style={{ backgroundColor:"#E5E7EB", paddingVertical:6, paddingHorizontal:10, borderRadius:999, marginRight:8, marginBottom:8 }}>
            <Text style={{ color:"#111" }}>{t}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}
