import React, { useContext } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import AppState from "../context/AppState";
const TEMPLATES = ["colorful","cool","minimal","professional","playful"];
export default function TemplatePicker(){
  const { selectedTemplate, setSelectedTemplate } = useContext(AppState);
  return (
    <View style={{ gap:8 }}>
      <Text style={{ fontWeight:"700", fontSize:16 }}>Choose a template</Text>
      <View style={{ flexDirection:"row", flexWrap:"wrap" }}>
        {TEMPLATES.map(t => (
          <TouchableOpacity key={t} onPress={()=>setSelectedTemplate(t)}
            style={{ paddingVertical:8, paddingHorizontal:12, borderRadius:999, marginRight:8, marginBottom:8,
                     backgroundColor: selectedTemplate===t ? "#6D5EF7" : "#E8E8EE" }}>
            <Text style={{ color: selectedTemplate===t ? "#fff" : "#222" }}>{t}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}
