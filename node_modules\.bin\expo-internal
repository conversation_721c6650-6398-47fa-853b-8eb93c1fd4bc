#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/@expo/cli/build/bin/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/@expo/cli/build/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/@expo/cli/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/@expo/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules:/mnt/c/Users/<USER>/Downloads/node_modules:/mnt/c/Users/<USER>/node_modules:/mnt/c/Users/<USER>/mnt/c/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/@expo/cli/build/bin/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/@expo/cli/build/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/@expo/cli/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/@expo/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules:/mnt/c/Users/<USER>/Downloads/node_modules:/mnt/c/Users/<USER>/node_modules:/mnt/c/Users/<USER>/mnt/c/node_modules:/mnt/c/Users/<USER>/Downloads/OneClickUi/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@expo/cli/build/bin/cli" "$@"
else
  exec node  "$basedir/../@expo/cli/build/bin/cli" "$@"
fi
