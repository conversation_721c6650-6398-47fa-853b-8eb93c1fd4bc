import { useEffect, useMemo, useState } from 'react';
import { View, Text, TextInput, ScrollView, StyleSheet, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import PrimaryButton from '../components/PrimaryButton';
import TemplatePicker from '../components/TemplatePicker';
import DomainChip from '../components/DomainChip';
import Card from '../components/Card';
import { suggestDomains, buildSite, checkAvailability, downloadZip } from '../lib/api';
import { generateLogo } from '../lib/logo';
import { useSites } from '../lib/SiteContext';

export default function Home() {
  const [idea, setIdea] = useState('budget-friendly online pharmacy for Kenyan university students');
  const [domains, setDomains] = useState([]);
  const [availability, setAvailability] = useState({}); // {domain:'available'|'taken'|'unknown'}
  const [picked, setPicked] = useState(null);
  const [style, setStyle] = useState('colorful');
  const [loading, setLoading] = useState(false);
  const [logoPrompt, setLogoPrompt] = useState('');
  const [logoLoading, setLogoLoading] = useState(false);
  const [logo, setLogo] = useState(null);
  const router = useRouter();
  const { addSite } = useSites();

  async function onSuggest() {
    setLoading(true);
    try {
      const r = await suggestDomains(idea, { bilingual:true, locale:'en-KE' });
      const list = r.domainIdeas || [];
      setDomains(list);
      setPicked(null);
      setAvailability({});
      // fire-and-forget availability checks
      list.forEach(async d => {
        const res = await checkAvailability(d);
        setAvailability(prev => ({ ...prev, [d]: res.available===true?'available':res.available===false?'taken':'unknown' }));
      });
    } catch(e) { alert(e?.message || String(e)); } 
    finally { setLoading(false); }
  }

  async function onGenerate() {
    if (!picked) return;
    setLoading(true);
    try {
      const res = await buildSite({ domain:picked, businessName: inferBusinessName(picked), templateStyle: style, asZip:false });
      addSite({ domain:picked, templateStyle:style, files: res.files, logoDataUrl: logo || undefined });
      router.push('/explore');
    } catch(e){ alert(e?.message || String(e)); }
    finally { setLoading(false); }
  }

  async function onGenLogo(){
    setLogoLoading(true);
    try { 
      const prompt = logoPrompt || `${inferBusinessName(picked || 'OneClick')} logo`;
      const img = await generateLogo(prompt);
      setLogo(img);
    } catch(e){ alert(e?.message||String(e)); } 
    finally { setLogoLoading(false); }
  }

  async function onDownloadZip(){
    if (!picked) return;
    try {
      const url = await downloadZip({ domain:picked, businessName: inferBusinessName(picked), templateStyle: style });
      if (Platform.OS === 'web') {
        const a = document.createElement('a');
        a.href = url;
        a.download = `${picked}.zip`;
        document.body.appendChild(a); a.click(); a.remove();
        setTimeout(()=> URL.revokeObjectURL(url), 2000);
      } else {
        alert('Download ZIP is web-only in this demo');
      }
    } catch(e){ alert(e?.message||String(e)); }
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>OneClick</Text>

      <Card>
        <Text style={styles.label}>Your idea</Text>
        <TextInput
          style={styles.input}
          value={idea}
          onChangeText={setIdea}
          placeholder="Describe the business…"
          placeholderTextColor="#888b93"
          multiline
        />
        <PrimaryButton title={loading ? 'Thinking…' : 'Suggest .KE domains'} onPress={onSuggest} disabled={loading} />
      </Card>

      {domains.length>0 && (
        <Card>
          <Text style={styles.h2}>Pick a domain</Text>
          <View style={styles.rowWrap}>
            {domains.map(d => (
              <DomainChip key={d} label={d} selected={picked===d} onPress={()=>setPicked(d)} badge={availability[d]} />
            ))}
          </View>

          <TemplatePicker value={style} onChange={setStyle} />
          <View style={{height:8}} />
          <PrimaryButton title={loading ? 'Building…' : (picked ? `Generate site for ${picked}` : 'Pick a domain')} onPress={onGenerate} disabled={!picked || loading} />
          <View style={{height:8}} />
          <PrimaryButton title="Download ZIP" onPress={onDownloadZip} disabled={!picked} />
        </Card>
      )}

      <Card>
        <Text style={styles.h2}>AI Logo (optional)</Text>
        <Text style={styles.label}>Describe a logo idea</Text>
        <TextInput
          style={styles.input}
          value={logoPrompt}
          onChangeText={setLogoPrompt}
          placeholder="e.g., 'StudentPharm: pill + book icon, purple gradient'"
          placeholderTextColor="#888b93"
        />
        <PrimaryButton title={logoLoading ? 'Generating…' : 'Generate Logo'} onPress={onGenLogo} disabled={logoLoading} />
        {logo && (
          <View style={{alignItems:'center', marginTop:12}}>
            <img alt="logo" src={logo} style={{width:160, height:160, borderRadius:16, border:'1px solid #22232c'}} />
          </View>
        )}
      </Card>
    </ScrollView>
  );
}

function inferBusinessName(domain) {
  const base = domain.replace(/\.(co\.ke|me\.ke|or\.ke|ac\.ke|go\.ke|sc\.ke|info\.ke|ne\.ke|ke)$/i,'')
  return base.split(/[^\w]+/).map(s => s.charAt(0).toUpperCase()+s.slice(1)).join(' ');
}

const styles = StyleSheet.create({
  container:{ padding:16, gap:16, backgroundColor:'#0b0b0e', minHeight:'100%' },
  title:{ color:'#e9e9f0', fontSize:22, fontWeight:'700' },
  h2:{ color:'#e9e9f0', fontSize:16, fontWeight:'700' },
  label:{ color:'#9aa0a6', marginBottom:8 },
  input:{ color:'#e9e9f0', backgroundColor:'#111218', borderRadius:12, borderWidth:1, borderColor:'#22232c', padding:12, minHeight:44 },
  rowWrap:{ flexDirection:'row', flexWrap:'wrap', gap:8 },
});
