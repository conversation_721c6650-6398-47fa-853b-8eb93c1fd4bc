import { useMemo, useState } from 'react';
import { View, Text, ScrollView, StyleSheet, Pressable, Platform } from 'react-native';
import { useSites } from '../lib/SiteContext';
import Card from '../components/Card';

export default function Explore() {
  const { sites } = useSites();
  const [activeId, setActiveId] = useState(sites[0]?.id ?? null);
  const active = useMemo(()=> sites.find(s => s.id===activeId) || sites[0], [sites, activeId]);

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>Explore</Text>
      {sites.length===0 ? (
        <Text style={styles.note}>Generate a site on the Home tab to see it here.</Text>
      ) : (
        <>
          <Card>
            <Text style={styles.h2}>Your generated sites</Text>
            <View style={styles.rowWrap}>
              {sites.map(s => (
                <Pressable key={s.id} onPress={()=>setActiveId(s.id)} style={[styles.tag, active?.id===s.id && styles.active]}>
                  <Text style={styles.tagTxt}>{s.domain} — {s.templateStyle}</Text>
                </Pressable>
              ))}
            </View>
          </Card>

          {active && (
            <Card>
              <Text style={styles.h2}>Preview — {active.domain}</Text>
              {active.logoDataUrl && (
                <View style={{alignItems:'center', marginBottom:12}}>
                  <img alt="logo" src={active.logoDataUrl} style={{width:120, height:120, borderRadius:12, border:'1px solid #22232c'}} />
                </View>
              )}
              {Platform.OS === 'web' ? (
                <iframe title="preview" style={{width:'100%',height:480,borderRadius:12,border:'1px solid #22232c'}} srcDoc={active.files['index.html'] || '<h1>No index.html</h1>'} />
              ) : (
                <Text style={styles.note}>Preview is available on Web. On mobile, export ZIP from backend.</Text>
              )}
            </Card>
          )}
        </>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container:{ padding:16, gap:16, backgroundColor:'#0b0b0e', minHeight:'100%' },
  title:{ color:'#e9e9f0', fontSize:22, fontWeight:'700' },
  h2:{ color:'#e9e9f0', fontSize:16, fontWeight:'700' },
  note:{ color:'#9aa0a6' },
  rowWrap:{ flexDirection:'row', flexWrap:'wrap', gap:8 },
  tag:{ paddingVertical:8, paddingHorizontal:12, borderRadius:999, backgroundColor:'#22232c' },
  active:{ backgroundColor:'#7c5cff' },
  tagTxt:{ color:'#e9e9f0', fontSize:13 },
});
