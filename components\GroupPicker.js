import React, { useContext } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import AppState from "../context/AppState";

const GROUPS = [
  ["student_startup","Students"],
  ["sme","SME"],
  ["creative","Creative"],
  ["nonprofit","Non-profit"],
  ["ecommerce","E-commerce"]
];

export default function GroupPicker(){
  const { group, setGroup } = useContext(AppState);
  return (
    <View style={{ gap:8 }}>
      <Text style={{ fontWeight:"700", fontSize:16 }}>Pick a recommendation group</Text>
      <View style={{ flexDirection:"row", flexWrap:"wrap" }}>
        {GROUPS.map(([k,l]) => (
          <TouchableOpacity key={k} onPress={()=>setGroup(k)}
            style={{ paddingVertical:8, paddingHorizontal:12, borderRadius:999, marginRight:8, marginBottom:8,
                     backgroundColor: group===k ? "#6D5EF7" : "#E8E8EE" }}>
            <Text style={{ color: group===k ? "#fff" : "#222" }}>{l}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
}
