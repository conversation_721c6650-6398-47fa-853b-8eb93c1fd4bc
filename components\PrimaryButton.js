import React from "react";
import { TouchableOpacity, Text } from "react-native";
export default function PrimaryButton({ label, onPress, disabled }){
  return (
    <TouchableOpacity onPress={onPress} disabled={disabled}
      style={{ backgroundColor: disabled ? "#9CA3AF" : "#6D5EF7", padding:12, borderRadius:10, marginVertical:6 }}>
      <Text style={{ color:"#fff", textAlign:"center", fontWeight:"600" }}>{label}</Text>
    </TouchableOpacity>
  );
}
