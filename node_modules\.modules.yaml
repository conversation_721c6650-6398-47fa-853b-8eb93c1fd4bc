hoistPattern:
  - '*'
hoistedDependencies:
  '@0no-co/graphql.web@1.2.0':
    '@0no-co/graphql.web': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.10.4':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.3':
    '@babel/core': private
  '@babel/generator@7.28.3':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.3)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.3)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.3)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.3)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.28.3':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.28.3':
    '@babel/helpers': private
  '@babel/highlight@7.25.9':
    '@babel/highlight': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-proposal-export-default-from@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-proposal-export-default-from': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.3)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-dynamic-import': private
  '@babel/plugin-syntax-export-default-from@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-export-default-from': private
  '@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-flow': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-classes@7.28.3(@babel/core@7.28.3)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-flow-strip-types@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-flow-strip-types': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.3)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-react-display-name@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-display-name': private
  '@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx-development': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-jsx': private
  '@babel/plugin-transform-react-pure-annotations@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-react-pure-annotations': private
  '@babel/plugin-transform-regenerator@7.28.3(@babel/core@7.28.3)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-runtime@7.28.3(@babel/core@7.28.3)':
    '@babel/plugin-transform-runtime': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.3)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/preset-react@7.27.1(@babel/core@7.28.3)':
    '@babel/preset-react': private
  '@babel/preset-typescript@7.27.1(@babel/core@7.28.3)':
    '@babel/preset-typescript': private
  '@babel/runtime@7.28.3':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.3':
    '@babel/traverse': private
    '@babel/traverse--for-generate-function-map': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@expo/cli@0.24.21':
    '@expo/cli': private
  '@expo/code-signing-certificates@0.0.5':
    '@expo/code-signing-certificates': private
  '@expo/config-plugins@10.1.2':
    '@expo/config-plugins': private
  '@expo/config-types@53.0.5':
    '@expo/config-types': private
  '@expo/config@11.0.13':
    '@expo/config': private
  '@expo/devcert@1.2.0':
    '@expo/devcert': private
  '@expo/env@1.0.7':
    '@expo/env': private
  '@expo/fingerprint@0.13.4':
    '@expo/fingerprint': private
  '@expo/image-utils@0.7.6':
    '@expo/image-utils': private
  '@expo/json-file@9.1.5':
    '@expo/json-file': private
  '@expo/metro-config@0.20.17':
    '@expo/metro-config': private
  '@expo/metro-runtime@5.0.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))':
    '@expo/metro-runtime': private
  '@expo/osascript@2.2.5':
    '@expo/osascript': private
  '@expo/package-manager@1.8.6':
    '@expo/package-manager': private
  '@expo/plist@0.3.5':
    '@expo/plist': private
  '@expo/prebuild-config@9.0.11':
    '@expo/prebuild-config': private
  '@expo/schema-utils@0.1.0':
    '@expo/schema-utils': private
  '@expo/sdk-runtime-versions@1.0.0':
    '@expo/sdk-runtime-versions': private
  '@expo/server@0.6.3':
    '@expo/server': private
  '@expo/spawn-async@1.7.2':
    '@expo/spawn-async': private
  '@expo/sudo-prompt@9.3.2':
    '@expo/sudo-prompt': private
  '@expo/vector-icons@14.1.0(expo-font@13.3.2(expo@53.0.22(@babel/core@7.28.3)(@expo/metro-runtime@5.0.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0)':
    '@expo/vector-icons': private
  '@expo/ws-tunnel@1.0.6':
    '@expo/ws-tunnel': private
  '@expo/xcpretty@4.3.2':
    '@expo/xcpretty': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@isaacs/ttlcache@1.4.1':
    '@isaacs/ttlcache': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/create-cache-key-function@29.7.0':
    '@jest/create-cache-key-function': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.11':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/react-compose-refs@1.1.2(react@19.0.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-slot@1.2.0(react@19.0.0)':
    '@radix-ui/react-slot': private
  '@react-native/assets-registry@0.79.5':
    '@react-native/assets-registry': private
  '@react-native/babel-plugin-codegen@0.79.6(@babel/core@7.28.3)':
    '@react-native/babel-plugin-codegen': private
  '@react-native/babel-preset@0.79.6(@babel/core@7.28.3)':
    '@react-native/babel-preset': private
  '@react-native/codegen@0.79.5(@babel/core@7.28.3)':
    '@react-native/codegen': private
  '@react-native/community-cli-plugin@0.79.5':
    '@react-native/community-cli-plugin': private
  '@react-native/debugger-frontend@0.79.5':
    '@react-native/debugger-frontend': private
  '@react-native/dev-middleware@0.79.6':
    '@react-native/dev-middleware': private
  '@react-native/gradle-plugin@0.79.5':
    '@react-native/gradle-plugin': private
  '@react-native/js-polyfills@0.79.5':
    '@react-native/js-polyfills': private
  '@react-native/normalize-colors@0.74.89':
    '@react-native/normalize-colors': private
  '@react-native/virtualized-lists@0.79.5(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0)':
    '@react-native/virtualized-lists': private
  '@react-navigation/bottom-tabs@7.4.7(@react-navigation/native@7.1.17(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.6.1(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native-screens@4.15.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0)':
    '@react-navigation/bottom-tabs': private
  '@react-navigation/core@7.12.4(react@19.0.0)':
    '@react-navigation/core': private
  '@react-navigation/elements@2.6.4(@react-navigation/native@7.1.17(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.6.1(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0)':
    '@react-navigation/elements': private
  '@react-navigation/native-stack@7.3.26(@react-navigation/native@7.1.17(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.6.1(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native-screens@4.15.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0)':
    '@react-navigation/native-stack': private
  '@react-navigation/native@7.1.17(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0)':
    '@react-navigation/native': private
  '@react-navigation/routers@7.5.1':
    '@react-navigation/routers': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/node-fetch@2.6.13':
    '@types/node-fetch': private
  '@types/node@18.19.123':
    '@types/node': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@urql/core@5.2.0':
    '@urql/core': private
  '@urql/exchange-retry@1.3.2(@urql/core@5.2.0)':
    '@urql/exchange-retry': private
  '@xmldom/xmldom@0.8.11':
    '@xmldom/xmldom': private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.4:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  anser@1.4.10:
    anser: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  archiver-utils@5.0.2:
    archiver-utils: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  asap@2.0.6:
    asap: private
  async-limiter@1.0.1:
    async-limiter: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  b4a@1.6.7:
    b4a: private
  babel-jest@29.7.0(@babel/core@7.28.3):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.3):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.3):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.3):
    babel-plugin-polyfill-regenerator: private
  babel-plugin-react-native-web@0.19.13:
    babel-plugin-react-native-web: private
  babel-plugin-syntax-hermes-parser@0.25.1:
    babel-plugin-syntax-hermes-parser: private
  babel-plugin-transform-flow-enums@0.0.2(@babel/core@7.28.3):
    babel-plugin-transform-flow-enums: private
  babel-preset-current-node-syntax@1.2.0(@babel/core@7.28.3):
    babel-preset-current-node-syntax: private
  babel-preset-expo@13.2.4(@babel/core@7.28.3):
    babel-preset-expo: private
  babel-preset-jest@29.6.3(@babel/core@7.28.3):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.6.1:
    bare-events: private
  base64-js@1.5.1:
    base64-js: private
  better-opn@3.0.2:
    better-opn: private
  big-integer@1.6.52:
    big-integer: private
  body-parser@1.20.3:
    body-parser: private
  bplist-creator@0.1.0:
    bplist-creator: private
  bplist-parser@0.3.2:
    bplist-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.4:
    browserslist: private
  bser@2.1.1:
    bser: private
  buffer-crc32@1.0.0:
    buffer-crc32: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  caller-callsite@2.0.0:
    caller-callsite: private
  caller-path@2.0.0:
    caller-path: private
  callsites@2.0.0:
    callsites: private
  camelcase@5.3.1:
    camelcase: private
  caniuse-lite@1.0.30001739:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chownr@3.0.0:
    chownr: private
  chrome-launcher@0.15.2:
    chrome-launcher: private
  chromium-edge-launcher@0.2.0:
    chromium-edge-launcher: private
  ci-info@3.9.0:
    ci-info: private
  cli-cursor@2.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  client-only@0.0.1:
    client-only: private
  cliui@8.0.1:
    cliui: private
  clone@1.0.4:
    clone: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@12.1.0:
    commander: private
  compress-commons@6.0.2:
    compress-commons: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.1:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  connect@3.7.0:
    connect: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  core-js-compat@3.45.1:
    core-js-compat: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@5.2.1:
    cosmiconfig: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@6.0.0:
    crc32-stream: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  css-in-js-utils@3.1.0:
    css-in-js-utils: private
  debug@2.6.9:
    debug: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  deep-extend@0.6.0:
    deep-extend: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-libc@1.0.3:
    detect-libc: private
  dotenv-expand@11.0.7:
    dotenv-expand: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.212:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  env-editor@0.4.2:
    env-editor: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  esprima@4.0.1:
    esprima: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  events@3.3.0:
    events: private
  exec-async@2.2.0:
    exec-async: private
  expo-asset@11.1.7(expo@53.0.22(@babel/core@7.28.3)(@expo/metro-runtime@5.0.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0):
    expo-asset: private
  expo-constants@17.1.7(expo@53.0.22(@babel/core@7.28.3)(@expo/metro-runtime@5.0.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)):
    expo-constants: private
  expo-file-system@18.1.11(expo@53.0.22(@babel/core@7.28.3)(@expo/metro-runtime@5.0.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)):
    expo-file-system: private
  expo-font@13.3.2(expo@53.0.22(@babel/core@7.28.3)(@expo/metro-runtime@5.0.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react@19.0.0):
    expo-font: private
  expo-keep-awake@14.1.4(expo@53.0.22(@babel/core@7.28.3)(@expo/metro-runtime@5.0.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react@19.0.0):
    expo-keep-awake: private
  expo-linking@7.1.7(expo@53.0.22(@babel/core@7.28.3)(@expo/metro-runtime@5.0.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0)))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0):
    expo-linking: private
  expo-modules-autolinking@2.1.14:
    expo-modules-autolinking: private
  expo-modules-core@2.5.0:
    expo-modules-core: private
  exponential-backoff@3.1.2:
    exponential-backoff: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fbjs-css-vars@1.0.2:
    fbjs-css-vars: private
  fbjs@3.0.5:
    fbjs: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  filter-obj@1.1.0:
    filter-obj: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flow-enums-runtime@0.0.6:
    flow-enums-runtime: private
  fontfaceobserver@2.3.0:
    fontfaceobserver: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.4:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  freeport-async@2.0.0:
    freeport-async: private
  fresh@0.5.2:
    fresh: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  getenv@2.0.0:
    getenv: private
  glob@7.2.3:
    glob: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hermes-estree@0.29.1:
    hermes-estree: private
  hermes-parser@0.25.1:
    hermes-parser: private
  hosted-git-info@7.0.2:
    hosted-git-info: private
  http-errors@2.0.0:
    http-errors: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  humanize-ms@1.2.1:
    humanize-ms: private
  hyphenate-style-name@1.1.0:
    hyphenate-style-name: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  image-size@1.2.1:
    image-size: private
  import-fresh@2.0.0:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inline-style-prefixer@7.0.1:
    inline-style-prefixer: private
  invariant@2.2.4:
    invariant: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-core-module@2.16.1:
    is-core-module: private
  is-directory@0.3.1:
    is-directory: private
  is-docker@2.2.1:
    is-docker: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-number@7.0.0:
    is-number: private
  is-stream@2.0.1:
    is-stream: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@5.2.1:
    istanbul-lib-instrument: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.4:
    jake: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-worker@29.7.0:
    jest-worker: private
  jimp-compact@0.16.1:
    jimp-compact: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsc-safe-url@0.2.4:
    jsc-safe-url: private
  jsesc@3.1.0:
    jsesc: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json5@2.2.3:
    json5: private
  kleur@3.0.3:
    kleur: private
  lan-network@0.1.7:
    lan-network: private
  lazystream@1.0.1:
    lazystream: private
  leven@3.1.0:
    leven: private
  lighthouse-logger@1.4.2:
    lighthouse-logger: private
  lightningcss-darwin-arm64@1.27.0:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.27.0:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.27.0:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.27.0:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.27.0:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.27.0:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.27.0:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.27.0:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.27.0:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.27.0:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.27.0:
    lightningcss: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.throttle@4.1.1:
    lodash.throttle: private
  lodash@4.17.21:
    lodash: private
  log-symbols@2.2.0:
    log-symbols: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  makeerror@1.0.12:
    makeerror: private
  marky@1.3.0:
    marky: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  memoize-one@6.0.0:
    memoize-one: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  methods@1.1.2:
    methods: private
  metro-babel-transformer@0.82.5:
    metro-babel-transformer: private
  metro-cache-key@0.82.5:
    metro-cache-key: private
  metro-cache@0.82.5:
    metro-cache: private
  metro-config@0.82.5:
    metro-config: private
  metro-core@0.82.5:
    metro-core: private
  metro-file-map@0.82.5:
    metro-file-map: private
  metro-minify-terser@0.82.5:
    metro-minify-terser: private
  metro-resolver@0.82.5:
    metro-resolver: private
  metro-runtime@0.82.5:
    metro-runtime: private
  metro-source-map@0.82.5:
    metro-source-map: private
  metro-symbolicate@0.82.5:
    metro-symbolicate: private
  metro-transform-plugins@0.82.5:
    metro-transform-plugins: private
  metro-transform-worker@0.82.5:
    metro-transform-worker: private
  metro@0.82.5:
    metro: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@1.2.0:
    mimic-fn: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  ms@2.0.0:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  negotiator@0.6.3:
    negotiator: private
  nested-error-stacks@2.0.1:
    nested-error-stacks: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  node-forge@1.3.1:
    node-forge: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-package-arg@11.0.3:
    npm-package-arg: private
  nullthrows@1.1.1:
    nullthrows: private
  ob1@0.82.5:
    ob1: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.1.0:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@2.0.1:
    onetime: private
  open@7.4.2:
    open: private
  ora@3.4.0:
    ora: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parse-json@4.0.0:
    parse-json: private
  parse-png@2.1.0:
    parse-png: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@3.0.1:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  plist@3.1.0:
    plist: private
  pngjs@3.4.0:
    pngjs: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.4.49:
    postcss: private
  pretty-bytes@5.6.0:
    pretty-bytes: private
  pretty-format@29.7.0:
    pretty-format: private
  proc-log@4.2.0:
    proc-log: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  progress@2.0.3:
    progress: private
  promise@8.3.0:
    promise: private
  prompts@2.4.2:
    prompts: private
  proxy-addr@2.0.7:
    proxy-addr: private
  punycode@2.3.1:
    punycode: private
  qrcode-terminal@0.11.0:
    qrcode-terminal: private
  qs@6.13.0:
    qs: private
  query-string@7.1.3:
    query-string: private
  queue@6.0.2:
    queue: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  rc@1.2.8:
    rc: private
  react-devtools-core@6.1.5:
    react-devtools-core: private
  react-fast-compare@3.2.2:
    react-fast-compare: private
  react-freeze@1.0.4(react@19.0.0):
    react-freeze: private
  react-is@18.3.1:
    react-is: private
  react-native-edge-to-edge@1.6.0(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0):
    react-native-edge-to-edge: private
  react-native-is-edge-to-edge@1.2.1(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0):
    react-native-is-edge-to-edge: private
  react-native-safe-area-context@5.6.1(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0):
    react-native-safe-area-context: private
  react-native-screens@4.15.4(react-native@0.79.5(@babel/core@7.28.3)(react@19.0.0))(react@19.0.0):
    react-native-screens: private
  react-refresh@0.14.2:
    react-refresh: private
  readable-stream@4.7.0:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.13.11:
    regenerator-runtime: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  requireg@0.2.2:
    requireg: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-workspace-root@2.0.0:
    resolve-workspace-root: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@2.0.0:
    restore-cursor: private
  rimraf@3.0.2:
    rimraf: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.4.1:
    sax: private
  scheduler@0.25.0:
    scheduler: private
  semver@7.6.3:
    semver: private
  send@0.19.0:
    send: private
  serialize-error@2.1.0:
    serialize-error: private
  serve-static@1.16.2:
    serve-static: private
  server-only@0.0.1:
    server-only: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-plist@1.3.1:
    simple-plist: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slugify@1.6.6:
    slugify: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.5.7:
    source-map: private
  split-on-first@1.1.0:
    split-on-first: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stack-utils@2.0.6:
    stack-utils: private
  stackframe@1.3.4:
    stackframe: private
  stacktrace-parser@0.1.11:
    stacktrace-parser: private
  statuses@2.0.1:
    statuses: private
  stream-buffers@2.2.0:
    stream-buffers: private
  streamx@2.22.1:
    streamx: private
  strict-uri-encode@2.0.0:
    strict-uri-encode: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-json-comments@2.0.1:
    strip-json-comments: private
  structured-headers@0.4.1:
    structured-headers: private
  styleq@0.1.3:
    styleq: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@2.3.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tar-stream@3.1.7:
    tar-stream: private
  tar@7.4.3:
    tar: private
  temp-dir@2.0.0:
    temp-dir: private
  terminal-link@2.1.1:
    terminal-link: private
  terser@5.43.1:
    terser: private
  test-exclude@6.0.0:
    test-exclude: private
  text-decoder@1.2.3:
    text-decoder: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throat@5.0.0:
    throat: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@0.7.1:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  ua-parser-js@1.0.41:
    ua-parser-js: private
  undici-types@5.26.5:
    undici-types: private
  undici@6.21.3:
    undici: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unique-string@2.0.0:
    unique-string: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.25.4):
    update-browserslist-db: private
  use-latest-callback@0.2.4(react@19.0.0):
    use-latest-callback: private
  use-sync-external-store@1.5.0(react@19.0.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@7.0.3:
    uuid: private
  validate-npm-package-name@5.0.1:
    validate-npm-package-name: private
  vary@1.1.2:
    vary: private
  vlq@1.0.1:
    vlq: private
  walker@1.0.8:
    walker: private
  warn-once@0.1.1:
    warn-once: private
  wcwidth@1.0.1:
    wcwidth: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@5.0.0:
    webidl-conversions: private
  whatwg-fetch@3.6.20:
    whatwg-fetch: private
  whatwg-url-without-unicode@8.0.0-3:
    whatwg-url-without-unicode: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  wonka@6.3.5:
    wonka: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  ws@8.18.3:
    ws: private
  xcode@3.0.1:
    xcode: private
  xml2js@0.6.0:
    xml2js: private
  xmlbuilder@15.1.1:
    xmlbuilder: private
  y18n@5.0.8:
    y18n: private
  yallist@5.0.0:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zip-stream@6.0.1:
    zip-stream: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.0
pendingBuilds: []
prunedAt: Mon, 01 Sep 2025 19:33:30 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.27.0
  - lightningcss-darwin-x64@1.27.0
  - lightningcss-freebsd-x64@1.27.0
  - lightningcss-linux-arm-gnueabihf@1.27.0
  - lightningcss-linux-arm64-gnu@1.27.0
  - lightningcss-linux-arm64-musl@1.27.0
  - lightningcss-linux-x64-gnu@1.27.0
  - lightningcss-linux-x64-musl@1.27.0
  - lightningcss-win32-arm64-msvc@1.27.0
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\Downloads\OneClickUi\node_modules\.pnpm
virtualStoreDirMaxLength: 120
