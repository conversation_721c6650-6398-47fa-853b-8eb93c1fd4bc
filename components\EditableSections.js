import React, { useContext } from "react";
import { View, Text, Switch } from "react-native";
import AppState from "../context/AppState";
export default function EditableSections(){
  const { siteConfig, setSiteConfig } = useContext(AppState);
  const pages = siteConfig?.pages || { home:true, about:true, contact:true, products:true };
  const toggle = (k) => setSiteConfig({ ...siteConfig, pages: { ...pages, [k]: !pages[k] }});
  return (
    <View style={{ gap: 8 }}>
      <Text style={{ fontWeight:"700" }}>Pages</Text>
      {Object.keys(pages).map(k => (
        <View key={k} style={{ flexDirection:"row", alignItems:"center", justifyContent:"space-between" }}>
          <Text style={{ textTransform:"capitalize" }}>{k}</Text>
          <Switch value={!!pages[k]} onValueChange={()=>toggle(k)} />
        </View>
      ))}
    </View>
  );
}
