// server/index.cjs
const express = require('express');
const cors = require('cors');
const { config } = require('dotenv');
const { OpenAI } = require('openai');
const ejs = require('ejs');
const archiver = require('archiver');
const path = require('node:path');
const fs = require('node:fs');
const { PassThrough } = require('node:stream');

config();

const app = express();
app.use(cors());
app.use(express.json());

const PORT = process.env.PORT || 8788;
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// ---------- utilities ----------

const STYLES = ['colorful', 'cool', 'minimal', 'sleek', 'playful'];
const PAGES = ['index', 'about', 'contact', 'products'];

function safeRead(file) {
  return fs.existsSync(file) ? fs.readFileSync(file, 'utf8') : '';
}

function defaultHTML(title, subtitle, logoDataUrl) {
  return `<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>${title} — Preview</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
<header class="hero">
  <div class="brand">
    ${logoDataUrl ? `<img class="logo" src="${logoDataUrl}" alt="logo"/>` : ''}
    <h1>${title}</h1>
    <p>${subtitle || ''}</p>
  </div>
</header>
<main class="container">
  <section>
    <h2>Hello</h2>
    <p>Your site is live.</p>
  </section>
</main>
<script src="script.js"></script>
</body>
</html>`;
}

function defaultCSS(styleName = 'cool') {
  const palette = {
    colorful: ['#ff6a00', '#ff00a8'],
    cool: ['#6366F1', '#22D3EE'],
    minimal: ['#111827', '#6B7280'],
    sleek: ['#0EA5E9', '#111827'],
    playful: ['#F59E0B', '#EC4899'],
  }[styleName] || ['#6366F1', '#22D3EE'];

  return `
:root{--a:${palette[0]};--b:${palette[1]};--bg:#0b0b0f;--fg:#e5e7eb;--panel:#14151c;--border:#242636}
*{box-sizing:border-box}html,body{margin:0;padding:0;background:var(--bg);color:var(--fg);font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto}
.hero{padding:48px;border-bottom:1px solid var(--border);background:linear-gradient(135deg,var(--a),var(--b));color:white}
.brand{display:flex;align-items:center;gap:16px}
.logo{width:64px;height:64px;border-radius:12px;object-fit:cover;box-shadow:0 6px 24px rgba(0,0,0,.25)}
.container{padding:32px}
section{background:var(--panel);border:1px solid var(--border);border-radius:16px;padding:24px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
h1,h2{margin:0 0 12px}
`;
}

function defaultJS() {
  return `console.log("Site loaded");`;
}

/** Render from templates/site/<style> if present; otherwise fallback */
function renderSite(style, data) {
  const root = path.resolve(process.cwd(), 'templates', 'site', style);
  const exists = fs.existsSync(root);
  const out = {};

  for (const p of PAGES) {
    const f = path.join(root, `${p}.ejs`);
    if (exists && fs.existsSync(f)) {
      out[`${p}.html`] = ejs.render(fs.readFileSync(f, 'utf8'), data, { async: false });
    } else if (p === 'index') {
      out['index.html'] = defaultHTML(
        data.title || data.businessName || data.domain,
        data.subtitle,
        data.logoDataUrl
      );
    } else {
      out[`${p}.html`] =
        `<!doctype html><html><head><meta charset="utf-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/><title>${p}</title><link rel="stylesheet" href="styles.css"/></head><body><main class="container"><h1>${p[0].toUpperCase()+p.slice(1)}</h1><p>Content coming soon.</p></main><script src="script.js"></script></body></html>`;
    }
  }

  const cssFile = path.join(root, 'styles.css');
  const jsFile  = path.join(root, 'script.js');
  out['styles.css'] = exists && fs.existsSync(cssFile) ? safeRead(cssFile) : defaultCSS(style);
  out['script.js']  = exists && fs.existsSync(jsFile)  ? safeRead(jsFile)  : defaultJS();

  return out;
}

function zipBundle(files) {
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', { zlib: { level: 9 } });
    const sink = new PassThrough();
    const chunks = [];
    sink.on('data', d => chunks.push(Buffer.from(d)));
    sink.on('end', () => resolve(Buffer.concat(chunks)));
    sink.on('error', reject);
    archive.on('error', reject);
    archive.pipe(sink);
    for (const [name, content] of Object.entries(files)) {
      archive.append(content, { name });
    }
    archive.finalize().catch(reject);
  });
}

// ---------- routes ----------

// health
app.get('/v1/health', (req, res) => res.json({ ok: true }));

// deterministic .KE suggestions
app.post('/v1/domains/suggest', (req, res) => {
  const { idea = '' } = req.body || {};
  if (!idea.trim()) {
    return res.status(400).json({ error: { code: 'bad_request', message: 'Required: idea' } });
  }
  const words = idea.toLowerCase().replace(/[^a-z0-9 ]/g, ' ').split(/\s+/).filter(Boolean);
  const seeds = Array.from(new Set([
    words.slice(0, 2).join(''),
    words.slice(-2).join(''),
    words.find(w => w.length > 6) || words[0] || 'brand',
    'student','uni','swift','friendly','budget','pharm','med','rx'
  ])).filter(Boolean);

  const zones = ['.co.ke', '.me.ke', '.ke'];
  const out = new Set();
  for (const s of seeds) for (const z of zones) out.add((s + z).replace(/\s+/g, ''));
  res.json({ domainIdeas: Array.from(out).slice(0, 20) });
});

// build site (preview or zip)
app.post('/v1/sites/generate', async (req, res) => {
  const { domain, businessName, templateStyle = 'cool', asZip = false, logoDataUrl = '' } = req.body || {};
  if (!domain || !businessName) {
    return res.status(400).json({ error: { code: 'bad_request', message: 'Required: domain, businessName' } });
  }
  const style = STYLES.includes(templateStyle) ? templateStyle : 'cool';

  const files = renderSite(style, {
    domain,
    businessName,
    title: businessName,
    subtitle: `Serving ${domain} with style.`,
    logoDataUrl
  });

  if (asZip) {
    const zip = await zipBundle(files);
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${domain}.zip"`);
    return res.send(zip);
  }
  res.json({ files });
});

// logo proxy via OpenAI (PNG data URL or URL fallback)
app.post('/v1/logo/generate', async (req, res) => {
  try {
    const { prompt } = req.body || {};
    if (!prompt) return res.status(400).json({ error: 'Missing prompt' });

    const r = await openai.images.generate({
      model: 'gpt-image-1',
      prompt: `${prompt}. Clean vector-style logomark on neutral background, modern and simple.`,
      size: '1024x1024'
    });

    const first = r?.data?.[0] || {};
    if (first.b64_json) return res.json({ dataUrl: `data:image/png;base64,${first.b64_json}` });
    if (first.url) return res.json({ dataUrl: first.url });
    return res.status(502).json({ error: 'no_image' });
  } catch (e) {
    const msg = String(e?.message || '');
    if (/hard limit/i.test(msg) || /quota/i.test(msg)) return res.status(402).json({ error: 'billing_limit' });
    return res.status(500).json({ error: 'logo_failed', message: msg });
  }
});

// proxy to OpenAI Responses API (keep simple; no text.format)
app.post('/v1/responses', async (req, res) => {
  try {
    const { model, messages, input } = req.body || {};
    const payload = {
      model: model || process.env.OPENAI_MODEL || 'gpt-4o-mini',
      input: input || messages || []
    };
    const r = await openai.responses.create(payload);
    res.status(200).json(r);
  } catch (e) {
    console.error('[/v1/responses] error:', e?.message || e);
    const status = e?.status || 500;
    res.status(status).json({ error: { message: String(e?.message || e) } });
  }
});

app.listen(PORT, () => {
  console.log(`Server ready http://127.0.0.1:${PORT}`);
});
