import React from "react";
import { Pressable, Text, View } from "react-native";
const badgeStyle = (status) => {
  switch (status) {
    case "Available": return { bg: "#DCFCE7", fg: "#166534" };
    case "Taken":     return { bg: "#FEE2E2", fg: "#991B1B" };
    default:          return { bg: "#E5E7EB", fg: "#374151" };
  }
};
export default function DomainChip({ domain, status="Unknown", selected=false, onPress }){
  const b = badgeStyle(status);
  return (
    <Pressable onPress={onPress}
      style={{ paddingVertical:8, paddingHorizontal:12, borderRadius:999,
               backgroundColor: selected ? "#6D5EF7" : "#111", borderWidth:1,
               borderColor: selected ? "#6D5EF7" : "#333", marginRight:8, marginBottom:8 }}>
      <Text style={{ color:"#fff" }}>{domain}</Text>
      <View style={{ alignSelf:"flex-start", marginTop:4, paddingHorizontal:8, paddingVertical:2,
                     borderRadius:999, backgroundColor:b.bg }}>
        <Text style={{ color:b.fg, fontSize:12 }}>{status}</Text>
      </View>
    </Pressable>
  );
}
