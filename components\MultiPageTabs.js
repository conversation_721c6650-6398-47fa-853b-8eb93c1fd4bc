import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
export default function MultiPageTabs({ current, setCurrent }){
  const tabs = ["index.html","about.html","contact.html","products.html"];
  return (
    <View style={{ flexDirection:"row", flexWrap:"wrap" }}>
      {tabs.map(t => (
        <TouchableOpacity key={t} onPress={()=>setCurrent(t)}
          style={{ paddingVertical:8, paddingHorizontal:12, borderRadius:8, marginRight:8, marginBottom:8,
                   backgroundColor: current===t ? "#6D5EF7" : "#E8E8EE" }}>
          <Text style={{ color: current===t ? "#fff" : "#222" }}>{t.replace(".html","")}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}
