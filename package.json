{"name": "OneClickUi", "version": "2.0.0", "private": true, "main": "expo-router/entry", "scripts": {"start": "expo start", "web": "expo start --web", "dev": "node server/index.cjs"}, "dependencies": {"archiver": "^7.0.1", "cors": "^2.8.5", "dotenv": "^16.6.1", "ejs": "^3.1.10", "expo": "53.0.22", "expo-router": "~5.1.5", "express": "^4.21.2", "openai": "^4.104.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-web": "^0.20.0"}}