import React from "react";
import { View } from "react-native";
import DomainChip from "./DomainChip";
export default function DomainPicker({ domains, status, selected, onPick }){
  return (
    <View style={{ flexDirection:"row", flexWrap:"wrap" }}>
      {domains.map((d) => (
        <DomainChip key={d} domain={d} status={status[d] || "Unknown"} selected={selected === d} onPress={()=>onPick(d)} />
      ))}
    </View>
  );
}
