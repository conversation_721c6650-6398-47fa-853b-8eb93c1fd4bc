import React, { createContext, useContext, useMemo, useState } from "react";

const AppState = createContext(null);
export default AppState;
export const useApp = () => useContext(AppState);

export function AppStateProvider({ children }) {
  const [group, setGroup] = useState("student_startup");
  const [selectedTemplate, setSelectedTemplate] = useState("playful");
  const [pickedDomain, setPickedDomain] = useState("");
  const [siteConfig, setSiteConfig] = useState({
    theme: { brandColor:"#6D5EF7", font:"Inter", radius:16, spacing:16, mode:"light" },
    pages: { home:true, about:true, contact:true, products:true },
    built: null
  });

  const value = useMemo(() => ({
    group, setGroup,
    selectedTemplate, setSelectedTemplate,
    pickedDomain, setPickedDomain,
    siteConfig, setSiteConfig
  }), [group, selectedTemplate, pickedDomain, siteConfig]);

  return <AppState.Provider value={value}>{children}</AppState.Provider>;
}
