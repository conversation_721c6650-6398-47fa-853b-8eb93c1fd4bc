import React, { useContext } from "react";
import { View, Text, TextInput } from "react-native";
import AppState from "../context/AppState";
export default function EditableThemePanel(){
  const { siteConfig, setSiteConfig } = useContext(AppState);
  const theme = siteConfig?.theme || { brandColor:"#6D5EF7", font:"Inter", radius:16, spacing:16 };
  const set = (patch) => setSiteConfig({ ...siteConfig, theme: { ...theme, ...patch }});
  return (
    <View style={{ gap:10 }}>
      <Text style={{ fontWeight:"700", fontSize:16 }}>Template settings</Text>
      <Text>Brand color</Text>
      <TextInput value={theme.brandColor} onChangeText={(t)=>set({brandColor:t})}
        placeholder="#6D5EF7" style={{ borderWidth:1, padding:8, borderRadius:8 }} />
      <Text>Font</Text>
      <TextInput value={theme.font} onChangeText={(t)=>set({font:t})}
        placeholder="Inter" style={{ borderWidth:1, padding:8, borderRadius:8 }} />
      <Text>Corner radius</Text>
      <TextInput value={String(theme.radius)} keyboardType="numeric" onChangeText={(t)=>set({radius:Number(t)||0})}
        placeholder="16" style={{ borderWidth:1, padding:8, borderRadius:8 }} />
      <Text>Section spacing (px)</Text>
      <TextInput value={String(theme.spacing)} keyboardType="numeric" onChangeText={(t)=>set({spacing:Number(t)||0})}
        placeholder="16" style={{ borderWidth:1, padding:8, borderRadius:8 }} />
    </View>
  );
}
